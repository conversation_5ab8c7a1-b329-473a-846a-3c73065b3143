import { GoogleGenerativeAI } from '@google/generative-ai';
import { Level, Language, FullQuestion } from '@/types';

const API_KEYS_STRING = process.env.GEMINI_API_KEY;

if (!API_KEYS_STRING) {
  throw new Error('Please define the GEMINI_API_KEY environment variable inside .env.local');
}

// Parse multiple API keys separated by commas
const API_KEYS = API_KEYS_STRING.split(',').map(key => key.trim()).filter(key => key.length > 0);

if (API_KEYS.length === 0) {
  throw new Error('No valid API keys found in GEMINI_API_KEY environment variable');
}

console.log(`🔑 Loaded ${API_KEYS.length} Gemini API key(s)`);

// Function to get a random API key
function getRandomApiKey(): string {
  const randomIndex = Math.floor(Math.random() * API_KEYS.length);
  const selectedKey = API_KEYS[randomIndex];
  console.log(`🎲 Using API key ${randomIndex + 1}/${API_KEYS.length} (${selectedKey.substring(0, 10)}...)`);
  return selectedKey;
}



// Cache to store recently generated questions to avoid repetition
const questionCache = new Map<string, Set<string>>();
const CACHE_SIZE = 50; // Keep last 50 questions per level-language combination

function addToCache(level: Level, language: Language, questionText: string) {
  const key = `${level}-${language}`;
  if (!questionCache.has(key)) {
    questionCache.set(key, new Set());
  }

  const cache = questionCache.get(key)!;
  cache.add(questionText.toLowerCase().trim());

  // Keep cache size manageable
  if (cache.size > CACHE_SIZE) {
    const iterator = cache.values();
    const firstItem = iterator.next().value;
    if (firstItem) {
      cache.delete(firstItem);
    }
  }
}

function isQuestionCached(level: Level, language: Language, questionText: string): boolean {
  const key = `${level}-${language}`;
  const cache = questionCache.get(key);
  return cache ? cache.has(questionText.toLowerCase().trim()) : false;
}

// Function to generate content with retry mechanism using different API keys
async function generateContentWithRetry(prompt: string, maxRetries: number = 3): Promise<string> {
  const usedKeys = new Set<string>();

  for (let attempt = 0; attempt < maxRetries && usedKeys.size < API_KEYS.length; attempt++) {
    try {
      // Get a random key that hasn't been used in this request
      let apiKey: string;
      let attempts = 0;
      do {
        apiKey = getRandomApiKey();
        attempts++;
      } while (usedKeys.has(apiKey) && attempts < 10 && usedKeys.size < API_KEYS.length);

      usedKeys.add(apiKey);

      const genAI = new GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

      console.log(`🚀 Attempt ${attempt + 1}/${maxRetries} with key ${apiKey.substring(0, 10)}...`);

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      console.log(`✅ Successfully generated content with key ${apiKey.substring(0, 10)}...`);
      return text;

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ Error with API key ${Array.from(usedKeys).pop()?.substring(0, 10)}...:`, errorMessage);

      // Check if it's a rate limit error
      if (errorMessage.includes('429') || errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
        console.log(`⏳ Rate limit detected, trying with different API key...`);
        continue;
      }

      // If it's not a rate limit error, throw immediately
      throw error;
    }
  }

  throw new Error(`Failed to generate content after ${maxRetries} attempts with ${usedKeys.size} different API keys`);
}

export async function generateQuestionWithGemini(level: Level, language: Language): Promise<FullQuestion> {
  try {

    // Create language-specific prompt
    const languageInstructions = {
      en: "Generate the question and all content in English.",
      vi: "Generate the question in English but provide explanation in Vietnamese to help Vietnamese learners understand better.",
      zh: "Generate the question in English but provide explanation in Chinese (Simplified) to help Chinese learners understand better."
    };

    const levelDescriptions = {
      beginner: "A1-A2 level: basic vocabulary (1000-2000 words), simple present/past tenses, basic sentence structures, common prepositions",
      intermediate: "B1-B2 level: intermediate vocabulary (3000-4000 words), complex tenses, conditionals, phrasal verbs, reported speech, passive voice",
      advanced: "C1-C2 level: advanced vocabulary (5000+ words), sophisticated grammar, subjunctive mood, complex conditionals, academic/business English, idiomatic expressions"
    };



    // Generate variety factors to ensure unique content
    const randomSeed = Math.floor(Math.random() * 1000000);
    const currentTime = Date.now();

    // Diverse topic pools for each level
    const topicPools = {
      beginner: [
        "workplace communication", "travel and tourism", "shopping and services",
        "health and wellness", "food and dining", "transportation", "housing and accommodation",
        "technology basics", "education and learning", "family and relationships"
      ],
      intermediate: [
        "business negotiations", "project management", "customer relations", "marketing strategies",
        "financial planning", "environmental sustainability", "cultural diversity", "innovation and creativity",
        "professional development", "international trade", "digital transformation", "team leadership"
      ],
      advanced: [
        "corporate governance", "strategic planning", "economic policy", "research methodology",
        "academic discourse", "diplomatic relations", "intellectual property", "risk management",
        "organizational psychology", "global economics", "policy analysis", "executive decision-making",
        "cross-cultural communication", "sustainable development", "technological disruption"
      ]
    };

    const randomTopic = topicPools[level][Math.floor(Math.random() * topicPools[level].length)];

    const prompt = `
You are an expert TOEIC/IELTS test teacher. Create ONE concise, focused multiple-choice question for ${level} English learners.

CRITICAL REQUIREMENTS:
1. KEEP IT SHORT: Questions must be concise and direct, like real TOEIC/IELTS questions
2. NO REPETITION: Use seed (${randomSeed}) and timestamp (${currentTime}) for unique content
3. FOCUS ON ONE SKILL: Test only ONE specific language point (vocabulary, grammar, idiom, etc.)

Level: ${level} (${levelDescriptions[level]})
Target: ${language === 'en' ? 'Native speakers' : language === 'vi' ? 'Vietnamese learners' : 'Chinese learners'}
Topic Area: ${randomTopic}

QUESTION TYPES TO CREATE:
${level === 'beginner' ? `
- Vocabulary in context (choose the correct word)
- Basic grammar (verb forms, prepositions, articles)
- Simple sentence completion
- Common collocations
- Basic phrasal verbs
` : level === 'intermediate' ? `
- Advanced vocabulary and synonyms
- Grammar structures (conditionals, passive voice, reported speech)
- Phrasal verbs and idioms
- Word formation (prefixes, suffixes)
- Collocations and word partnerships
` : `
- Sophisticated vocabulary and nuanced meanings
- Complex grammar (subjunctive, inversion, advanced conditionals)
- Idiomatic expressions and metaphors
- Register and formality levels
- Advanced collocations and academic language
`}

FORMAT EXAMPLES:
Beginner: "The meeting will _____ at 3 PM." (start/begin/commence/initiate)
Intermediate: "The project was _____ due to budget constraints." (postponed/delayed/deferred/suspended)
Advanced: "The proposal was met with _____ from the board." (skepticism/dubiety/incredulity/wariness)

CONSTRUCTION RULES:
1. ONE sentence or short phrase only
2. Test ONE specific language point clearly
3. All options must be grammatically possible
4. Only ONE option is contextually/semantically correct
5. Avoid long scenarios or complex contexts
6. ${languageInstructions[language]}

DISTRACTOR GUIDELINES:
- Use words of similar meaning but wrong context
- Include common learner errors for the level
- Make all options plausible at first glance
- Test precise understanding of word usage

OUTPUT FORMAT (Valid JSON only):
{
  "question": "Short, direct question testing one language point",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correctAnswer": 0,
  "explanation": "Translate full the sentence into ${language === 'en' ? 'English' : language === 'vi' ? 'Vietnamese' : 'Chinese (Simplified)'} and 
  provide Brief explanation of why the answer is correct in ${language === 'en' ? 'English' : language === 'vi' ? 'Vietnamese' : 'Chinese (Simplified)'}"
}

UNIQUENESS: Use seed ${randomSeed} to avoid repetition. Create original content, not textbook examples.
`;

    // Try up to 3 times to generate a unique question
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      const text = await generateContentWithRetry(prompt);

      // Clean up the response to extract JSON
      let jsonText = text.trim();

      // Remove markdown code blocks if present
      if (jsonText.startsWith('```json')) {
        jsonText = jsonText.replace(/```json\n?/, '').replace(/\n?```$/, '');
      } else if (jsonText.startsWith('```')) {
        jsonText = jsonText.replace(/```\n?/, '').replace(/\n?```$/, '');
      }

      // Parse the JSON response
      const questionData = JSON.parse(jsonText);

      // Validate the response structure
      if (!questionData.question || !Array.isArray(questionData.options) ||
          questionData.options.length !== 4 ||
          typeof questionData.correctAnswer !== 'number' ||
          questionData.correctAnswer < 0 || questionData.correctAnswer > 3 ||
          !questionData.explanation) {
        throw new Error('Invalid question format from Gemini');
      }

      // Check if this question was recently generated
      if (!isQuestionCached(level, language, questionData.question)) {
        // Add to cache and return the unique question
        addToCache(level, language, questionData.question);

        // Generate unique ID
        const id = Math.random().toString(36).substring(2, 11);

        return {
          id,
          question: questionData.question,
          options: questionData.options,
          correctAnswer: questionData.correctAnswer,
          explanation: questionData.explanation,
          level
        };
      }

      attempts++;
      console.log(`Question was cached, retrying... (attempt ${attempts}/${maxAttempts})`);
    }

    // If all attempts failed to generate unique question, proceed with the last one
    console.log('Max attempts reached, using last generated question');
    const text = await generateContentWithRetry(prompt);

    let jsonText = text.trim();
    if (jsonText.startsWith('```json')) {
      jsonText = jsonText.replace(/```json\n?/, '').replace(/\n?```$/, '');
    } else if (jsonText.startsWith('```')) {
      jsonText = jsonText.replace(/```\n?/, '').replace(/\n?```$/, '');
    }

    const questionData = JSON.parse(jsonText);
    const id = Math.random().toString(36).substring(2, 11);
    addToCache(level, language, questionData.question);

    return {
      id,
      question: questionData.question,
      options: questionData.options,
      correctAnswer: questionData.correctAnswer,
      explanation: questionData.explanation,
      level
    };

  } catch (error) {
    console.error('Error generating question with Gemini:', error);
    
    // Fallback to mock question if Gemini fails
    console.log('Falling back to mock question generation...');
    return generateFallbackQuestion(level, language);
  }
}

// Fallback function in case Gemini API fails - Short TOEIC/IELTS style questions
function generateFallbackQuestion(level: Level, language: Language): FullQuestion {
  const mockQuestions = {
    beginner: {
      en: {
        question: "The meeting will _____ at 3 PM sharp.",
        options: ["start", "starting", "started", "to start"],
        correctAnswer: 0,
        explanation: "After 'will', we use the base form of the verb. 'Will start' is the correct future tense structure."
      },
      vi: {
        question: "The meeting will _____ at 3 PM sharp.",
        options: ["start", "starting", "started", "to start"],
        correctAnswer: 0,
        explanation: "Sau 'will', chúng ta sử dụng dạng nguyên thể của động từ. 'Will start' là cấu trúc thì tương lai đúng."
      },
      zh: {
        question: "The meeting will _____ at 3 PM sharp.",
        options: ["start", "starting", "started", "to start"],
        correctAnswer: 0,
        explanation: "在 'will' 后面，我们使用动词原形。'Will start' 是正确的将来时结构。"
      }
    },
    intermediate: {
      en: {
        question: "The project was _____ due to budget constraints.",
        options: ["postponed", "delayed", "deferred", "suspended"],
        correctAnswer: 0,
        explanation: "'Postponed' is the most appropriate word for officially rescheduling something to a later time due to specific reasons."
      },
      vi: {
        question: "The project was _____ due to budget constraints.",
        options: ["postponed", "delayed", "deferred", "suspended"],
        correctAnswer: 0,
        explanation: "'Postponed' là từ phù hợp nhất để chỉ việc chính thức hoãn lại một việc gì đó do lý do cụ thể."
      },
      zh: {
        question: "The project was _____ due to budget constraints.",
        options: ["postponed", "delayed", "deferred", "suspended"],
        correctAnswer: 0,
        explanation: "'Postponed' 是最合适的词，表示由于特定原因正式推迟某事。"
      }
    },
    advanced: {
      en: {
        question: "The proposal was met with _____ from the board members.",
        options: ["skepticism", "dubiety", "incredulity", "wariness"],
        correctAnswer: 0,
        explanation: "'Skepticism' is the most natural and commonly used word in business contexts to express doubt about a proposal."
      },
      vi: {
        question: "The proposal was met with _____ from the board members.",
        options: ["skepticism", "dubiety", "incredulity", "wariness"],
        correctAnswer: 0,
        explanation: "'Skepticism' là từ tự nhiên và thường được sử dụng nhất trong bối cảnh kinh doanh để diễn tả sự nghi ngờ về một đề xuất."
      },
      zh: {
        question: "The proposal was met with _____ from the board members.",
        options: ["skepticism", "dubiety", "incredulity", "wariness"],
        correctAnswer: 0,
        explanation: "'Skepticism' 是在商业语境中最自然、最常用的词，用来表达对提案的怀疑。"
      }
    }
  };

  const questionData = mockQuestions[level][language];
  const id = Math.random().toString(36).substring(2, 11);

  return {
    id,
    level,
    ...questionData
  };
}

export async function generateWritingPromptWithGemini(
  level: Level,
  language: Language,
  topic?: string,
  type: string = 'essay'
): Promise<any> {
  try {
    const levelDescriptions = {
      beginner: "simple vocabulary and basic sentence structures (A1-A2 level)",
      intermediate: "moderate vocabulary and complex sentences (B1-B2 level)",
      advanced: "sophisticated vocabulary and complex structures (C1-C2 level)"
    };

    const languageInstructions = {
      en: "Provide instructions and tips in English",
      vi: "Provide instructions and tips in Vietnamese",
      zh: "Provide instructions and tips in Chinese"
    };

    const topicPrompt = topic ? `about the topic: ${topic}` : 'on any appropriate topic';

    const prompt = `Generate a writing prompt for ${level} level English learners to write a ${type} ${topicPrompt}.
    ${languageInstructions[language]}.

    The prompt should be appropriate for ${levelDescriptions[level]}.

    Return a JSON object with this structure:
    {
      "title": "Writing prompt title",
      "prompt": "The main writing prompt/question",
      "instructions": "Clear instructions for the student",
      "tips": ["Tip 1", "Tip 2", "Tip 3"],
      "wordCount": "suggested word count range",
      "timeLimit": "suggested time limit",
      "criteria": ["Evaluation criterion 1", "Evaluation criterion 2", "Evaluation criterion 3"],
      "sampleOpening": "Example of how to start the writing"
    }

    Make sure the prompt is engaging and appropriate for ${level} level learners.
    Return only the JSON object, no additional text.`;

    const text = await generateContentWithRetry(prompt);

    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    return JSON.parse(jsonMatch[0]);

  } catch (error) {
    console.error('Error generating writing prompt with Gemini:', error);
    throw new Error('Failed to generate writing prompt');
  }
}

export async function generateStudyPlanWithGemini(params: {
  level: Level;
  language: Language;
  goals: string[];
  timeAvailable: number;
  weakAreas: string[];
  preferredLearningStyle: string;
}): Promise<any> {
  try {
    const { level, language, goals, timeAvailable, weakAreas, preferredLearningStyle } = params;

    const languageInstructions = {
      en: "Provide the study plan in English",
      vi: "Provide the study plan in Vietnamese",
      zh: "Provide the study plan in Chinese"
    };

    const prompt = `Create a personalized English study plan for a ${level} level learner.
    ${languageInstructions[language]}.

    Student details:
    - Current level: ${level}
    - Goals: ${goals.join(', ') || 'General English improvement'}
    - Time available: ${timeAvailable} hours per week
    - Weak areas: ${weakAreas.join(', ') || 'Not specified'}
    - Learning style: ${preferredLearningStyle}

    Return a JSON object with this structure:
    {
      "overview": "Brief overview of the study plan",
      "duration": "Recommended duration (e.g., '12 weeks')",
      "weeklySchedule": {
        "totalHours": ${timeAvailable},
        "breakdown": {
          "grammar": "X hours",
          "vocabulary": "X hours",
          "reading": "X hours",
          "listening": "X hours",
          "speaking": "X hours",
          "writing": "X hours"
        }
      },
      "phases": [
        {
          "phase": "Phase 1",
          "duration": "Weeks 1-4",
          "focus": "Main focus areas",
          "goals": ["Goal 1", "Goal 2"],
          "activities": ["Activity 1", "Activity 2", "Activity 3"]
        }
      ],
      "dailyRoutine": {
        "warmUp": "5-10 minute warm-up activity",
        "mainStudy": "Main study activities",
        "practice": "Practice activities",
        "review": "Review activities"
      },
      "resources": ["Resource 1", "Resource 2", "Resource 3"],
      "milestones": [
        {
          "week": 4,
          "target": "What to achieve by week 4"
        }
      ],
      "tips": ["Tip 1", "Tip 2", "Tip 3"]
    }

    Make the plan specific, actionable, and tailored to the student's needs.
    Return only the JSON object, no additional text.`;

    const text = await generateContentWithRetry(prompt);

    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    return JSON.parse(jsonMatch[0]);

  } catch (error) {
    console.error('Error generating study plan with Gemini:', error);
    throw new Error('Failed to generate study plan');
  }
}

export async function generateExplanationWithGemini(params: {
  question: string;
  language: Language;
  context: string;
  explanationType: string;
}): Promise<any> {
  try {
    const { question, language, context, explanationType } = params;

    const languageInstructions = {
      en: "Provide the explanation in English",
      vi: "Provide the explanation in Vietnamese",
      zh: "Provide the explanation in Chinese"
    };

    const typeDescriptions: Record<string, string> = {
      grammar: "grammar rules and structures",
      vocabulary: "word meanings, usage, and etymology",
      pronunciation: "pronunciation, phonetics, and speaking tips",
      usage: "proper usage in different contexts",
      cultural: "cultural context and appropriateness",
      general: "general English language questions"
    };

    const prompt = `Answer this English learning question about ${typeDescriptions[explanationType] || typeDescriptions.general}.
    ${languageInstructions[language]}.

    Question: ${question}
    ${context ? `Context: ${context}` : ''}

    Return a JSON object with this structure:
    {
      "answer": "Direct answer to the question",
      "explanation": "Detailed explanation",
      "examples": ["Example 1", "Example 2", "Example 3"],
      "tips": ["Helpful tip 1", "Helpful tip 2"],
      "relatedTopics": ["Related topic 1", "Related topic 2"],
      "commonMistakes": ["Common mistake 1", "Common mistake 2"],
      "practiceExercises": ["Exercise suggestion 1", "Exercise suggestion 2"]
    }

    Provide a comprehensive, educational response that helps the learner understand the topic thoroughly.
    Return only the JSON object, no additional text.`;

    const text = await generateContentWithRetry(prompt);

    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    return JSON.parse(jsonMatch[0]);

  } catch (error) {
    console.error('Error generating explanation with Gemini:', error);
    throw new Error('Failed to generate explanation');
  }
}

export async function generateAssessmentWithGemini(params: {
  userId: string;
  quizResults: any[];
  userLevel: Level;
  language: Language;
  accuracy: number;
  mistakesByLevel: any;
  requestType: string;
}): Promise<any> {
  try {
    const {
      userId,
      quizResults,
      userLevel,
      language,
      accuracy,
      mistakesByLevel,
      requestType
    } = params;

    const languageInstructions = {
      en: "Provide the assessment in English",
      vi: "Provide the assessment in Vietnamese",
      zh: "Provide the assessment in Chinese"
    };

    // Prepare mistake analysis for AI
    const mistakeAnalysis = Object.entries(mistakesByLevel).map(([level, mistakes]: [string, any]) => {
      return `${level} level: ${mistakes.length} mistakes`;
    }).join(', ');

    const sampleMistakes = quizResults
      .filter(result => !result.isCorrect)
      .slice(0, 5)
      .map(mistake => `Question: "${mistake.question}" - Incorrect answer: "${mistake.userAnswer}" - Correct: "${mistake.correctAnswer}"`)
      .join('\n');

    const prompt = requestType === 'analysis'
      ? `Analyze this English learner's quiz performance and provide detailed feedback.
        ${languageInstructions[language]}.

        Student Profile:
        - Current Level: ${userLevel}
        - Quiz Accuracy: ${accuracy.toFixed(1)}%
        - Total Questions: ${quizResults.length}
        - Mistakes by Level: ${mistakeAnalysis}

        Sample Mistakes:
        ${sampleMistakes}

        Return a JSON object with this structure:
        {
          "overallPerformance": "Brief overall assessment",
          "strengths": ["Strength 1", "Strength 2", "Strength 3"],
          "weaknesses": ["Weakness 1", "Weakness 2", "Weakness 3"],
          "detailedAnalysis": {
            "grammar": "Analysis of grammar performance",
            "vocabulary": "Analysis of vocabulary performance",
            "comprehension": "Analysis of comprehension performance"
          },
          "levelAssessment": "Assessment of current level and readiness for next level",
          "improvementAreas": ["Area 1", "Area 2", "Area 3"],
          "motivationalMessage": "Encouraging message for the student"
        }`
      : `Generate personalized study recommendations based on this English learner's quiz performance.
        ${languageInstructions[language]}.

        Student Profile:
        - Current Level: ${userLevel}
        - Quiz Accuracy: ${accuracy.toFixed(1)}%
        - Total Questions: ${quizResults.length}
        - Mistakes by Level: ${mistakeAnalysis}

        Sample Mistakes:
        ${sampleMistakes}

        Return a JSON object with this structure:
        {
          "immediateActions": ["Action 1", "Action 2", "Action 3"],
          "studyPlan": {
            "daily": "Daily study recommendations",
            "weekly": "Weekly study goals",
            "monthly": "Monthly objectives"
          },
          "recommendedResources": [
            {
              "type": "lesson",
              "title": "Lesson title",
              "description": "Why this lesson is recommended",
              "priority": "high/medium/low"
            }
          ],
          "practiceExercises": [
            {
              "type": "exercise type",
              "description": "Exercise description",
              "frequency": "How often to practice"
            }
          ],
          "nextSteps": ["Step 1", "Step 2", "Step 3"],
          "timeEstimate": "Estimated time to see improvement"
        }`;

    const text = await generateContentWithRetry(prompt);

    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    return JSON.parse(jsonMatch[0]);

  } catch (error) {
    console.error('Error generating assessment with Gemini:', error);
    throw new Error('Failed to generate assessment');
  }
}

export async function generateReadingPassageWithGemini(params: {
  level: Level;
  language: Language;
  topic?: string;
  wordCount?: number;
  passageType?: string;
}): Promise<any> {
  try {
    const { level, language, topic, wordCount = 300, passageType = 'general' } = params;

    const levelDescriptions = {
      beginner: "simple vocabulary and basic sentence structures (A1-A2 level)",
      intermediate: "moderate vocabulary and complex sentences (B1-B2 level)",
      advanced: "sophisticated vocabulary and complex structures (C1-C2 level)"
    };

    const languageInstructions = {
      en: "Provide questions and explanations in English",
      vi: "Provide questions and explanations in Vietnamese",
      zh: "Provide questions and explanations in Chinese"
    };

    const topicPrompt = topic ? `about ${topic}` : 'on an interesting and educational topic';
    const typePrompt = passageType === 'news' ? 'news article style' :
                      passageType === 'story' ? 'narrative story style' :
                      passageType === 'academic' ? 'academic text style' : 'general informative style';

    const prompt = `Generate a reading comprehension passage for ${level} level English learners.
    ${languageInstructions[language]}.

    Requirements:
    - Topic: ${topicPrompt}
    - Style: ${typePrompt}
    - Length: approximately ${wordCount} words
    - Level: ${levelDescriptions[level]}
    - Include engaging and educational content

    Return a JSON object with this structure:
    {
      "title": "Engaging title for the passage",
      "passage": "The complete reading passage text",
      "wordCount": ${wordCount},
      "readingTime": "estimated reading time in minutes",
      "level": "${level}",
      "topic": "main topic/category",
      "vocabulary": [
        {
          "word": "challenging word from the passage",
          "definition": "clear definition",
          "context": "sentence from passage where word appears",
          "pronunciation": "phonetic pronunciation",
          "partOfSpeech": "noun/verb/adjective/etc"
        }
      ],
      "comprehensionQuestions": [
        {
          "id": "q1",
          "type": "multiple-choice",
          "question": "Question about main idea or details",
          "options": ["Option A", "Option B", "Option C", "Option D"],
          "correctAnswer": 0,
          "explanation": "Why this answer is correct",
          "difficulty": "easy/medium/hard"
        },
        {
          "id": "q2",
          "type": "true-false",
          "question": "True or false statement about the passage",
          "options": ["True", "False"],
          "correctAnswer": 0,
          "explanation": "Explanation with reference to passage",
          "difficulty": "easy/medium/hard"
        }
      ],
      "discussionQuestions": [
        "Open-ended question 1 for discussion",
        "Open-ended question 2 for discussion"
      ],
      "keyThemes": ["Theme 1", "Theme 2", "Theme 3"],
      "culturalNotes": "Any cultural context or background information if relevant"
    }

    Guidelines:
    - Include 5-8 vocabulary words appropriate for the level
    - Create 4-6 comprehension questions of varying difficulty
    - Mix question types (multiple choice, true/false, short answer)
    - Ensure vocabulary words are actually used in the passage
    - Make content engaging and educational
    - Include cultural context when relevant

    Return only the JSON object, no additional text.`;

    const text = await generateContentWithRetry(prompt);

    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    return JSON.parse(jsonMatch[0]);

  } catch (error) {
    console.error('Error generating reading passage with Gemini:', error);
    throw new Error('Failed to generate reading passage');
  }
}

export async function generateLessonWithGemini(params: {
  level: Level;
  language: Language;
  topic: string;
  lessonType: string;
  duration?: number;
}): Promise<any> {
  try {
    const { level, language, topic, lessonType, duration = 30 } = params;

    const levelDescriptions = {
      beginner: "simple vocabulary and basic concepts (A1-A2 level)",
      intermediate: "moderate vocabulary and complex concepts (B1-B2 level)",
      advanced: "sophisticated vocabulary and advanced concepts (C1-C2 level)"
    };

    const languageInstructions = {
      en: "Provide all content and explanations in English",
      vi: "Provide all content and explanations in Vietnamese",
      zh: "Provide all content and explanations in Chinese"
    };

    const typeDescriptions: Record<string, string> = {
      grammar: "grammar rules and structures",
      vocabulary: "vocabulary building and word usage",
      conversation: "conversational skills and dialogue practice",
      pronunciation: "pronunciation and phonetics",
      writing: "writing skills and composition",
      listening: "listening comprehension and audio skills",
      culture: "cultural understanding and context"
    };

    const prompt = `Create a comprehensive English lesson for ${level} level learners.
    ${languageInstructions[language]}.

    Lesson Requirements:
    - Topic: ${topic}
    - Type: ${lessonType} (${typeDescriptions[lessonType] || 'general English skills'})
    - Level: ${levelDescriptions[level]}
    - Duration: approximately ${duration} minutes
    - Interactive and engaging content

    Return a JSON object with this structure:
    {
      "title": "Engaging lesson title",
      "description": "Brief lesson description",
      "level": "${level}",
      "type": "${lessonType}",
      "topic": "${topic}",
      "duration": ${duration},
      "objectives": [
        "Learning objective 1",
        "Learning objective 2",
        "Learning objective 3"
      ],
      "sections": [
        {
          "id": "introduction",
          "title": "Introduction",
          "type": "explanation",
          "content": "Introduction content with key concepts",
          "duration": 5
        },
        {
          "id": "main-content",
          "title": "Main Content",
          "type": "explanation",
          "content": "Main lesson content with examples and explanations",
          "examples": [
            "Example 1 with explanation",
            "Example 2 with explanation"
          ],
          "duration": 15
        },
        {
          "id": "practice",
          "title": "Practice Exercises",
          "type": "exercise",
          "exercises": [
            {
              "id": "ex1",
              "type": "multiple-choice",
              "question": "Practice question 1",
              "options": ["Option A", "Option B", "Option C", "Option D"],
              "correctAnswer": 0,
              "explanation": "Why this answer is correct"
            },
            {
              "id": "ex2",
              "type": "fill-blank",
              "question": "Fill in the blank: The cat ___ on the mat.",
              "answer": "sits",
              "explanation": "Explanation of the correct answer"
            }
          ],
          "duration": 8
        },
        {
          "id": "summary",
          "title": "Summary & Review",
          "type": "summary",
          "content": "Key points summary and review",
          "keyPoints": [
            "Key point 1",
            "Key point 2",
            "Key point 3"
          ],
          "duration": 2
        }
      ],
      "vocabulary": [
        {
          "word": "important word from lesson",
          "definition": "clear definition",
          "example": "example sentence",
          "pronunciation": "phonetic pronunciation"
        }
      ],
      "homework": [
        "Homework assignment 1",
        "Homework assignment 2"
      ],
      "additionalResources": [
        "Resource 1 suggestion",
        "Resource 2 suggestion"
      ],
      "tips": [
        "Learning tip 1",
        "Learning tip 2"
      ]
    }

    Guidelines:
    - Make content appropriate for ${level} level
    - Include 3-5 practice exercises of different types
    - Provide clear explanations and examples
    - Include 5-8 key vocabulary words
    - Make content engaging and interactive
    - Ensure total duration matches requested time
    - Include cultural context when relevant

    Return only the JSON object, no additional text.`;

    const text = await generateContentWithRetry(prompt);

    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    return JSON.parse(jsonMatch[0]);

  } catch (error) {
    console.error('Error generating lesson with Gemini:', error);
    throw new Error('Failed to generate lesson');
  }
}
