'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Question, Level, Language, QuestionResult } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { useQuizScore } from '@/hooks/useQuizScore';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import QuestionCard from '@/components/QuestionCard';
import ResultCard from '@/components/ResultCard';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import SkeletonLoader from '@/components/SkeletonLoader';

interface QuizClientProps {
  initialQuestion: Question;
  level: Level;
  language: Language;
}

interface QuizState {
  currentQuestion: Question | null;
  selectedAnswer: number | null;
  showResult: boolean;
  isTransitioning: boolean;
  questionResult: QuestionResult | null;
}

export default function QuizClient({ initialQuestion, level, language }: QuizClientProps) {
  const router = useRouter();
  const { user } = useAuth();
  const { score, updateScore, resetScore } = useQuizScore(level);

  const [quizState, setQuizState] = useState<QuizState>({
    currentQuestion: initialQuestion,
    selectedAnswer: null,
    showResult: false,
    isTransitioning: false,
    questionResult: null
  });

  const [showAuthModal, setShowAuthModal] = useState(false);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [isLoading, setIsLoading] = useState(false);

  const handleAnswerSelect = async (answerIndex: number) => {
    if (quizState.showResult || !quizState.currentQuestion) return;

    setQuizState(prev => ({
      ...prev,
      selectedAnswer: answerIndex
    }));

    const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);

    try {
      // Submit answer to server for validation
      const response = await fetch('/api/check-answer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          questionId: quizState.currentQuestion.id,
          userAnswer: answerIndex
        }),
      });

      const data = await response.json();

      if (data.success && data.result) {
        const isCorrect = data.result.isCorrect;

        // Update quiz state with result
        setTimeout(() => {
          setQuizState(prev => ({
            ...prev,
            showResult: true,
            questionResult: data.result
          }));
          // Update score using the hook
          updateScore(isCorrect);
        }, 500);

        // Save to history if user is logged in
        if (user && quizState.currentQuestion) {
          try {
            await fetch('/api/quiz/history', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                questionId: quizState.currentQuestion.id,
                question: quizState.currentQuestion.question,
                options: quizState.currentQuestion.options,
                correctAnswer: data.result.correctAnswer,
                userAnswer: answerIndex,
                explanation: data.result.explanation,
                level: quizState.currentQuestion.level,
                language: language,
                timeSpent: timeSpent,
              }),
            });
          } catch (error) {
            console.error('Failed to save quiz history:', error);
          }
        }
      } else {
        console.error('Failed to submit answer:', data.error);
        return;
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      return;
    }
  };

  const handleNextQuestion = async () => {
    // Start transition immediately
    setQuizState(prev => ({
      ...prev,
      isTransitioning: true
    }));

    // Small delay to allow transition to start, then clear content
    setTimeout(() => {
      setQuizState(prev => ({
        ...prev,
        currentQuestion: null,
        selectedAnswer: null,
        showResult: false,
        questionResult: null
      }));
      setIsLoading(true);
    }, 150);

    try {
      const response = await fetch('/api/quiz/next-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ level, language }),
      });

      const data = await response.json();

      if (data.success && data.question) {
        setQuizState({
          currentQuestion: data.question,
          selectedAnswer: null,
          showResult: false,
          isTransitioning: false,
          questionResult: null
        });
        // Reset question start time for new question
        setQuestionStartTime(Date.now());
      } else {
        console.error('Failed to generate next question:', data.error);
        // Fallback to page refresh if API fails
        router.refresh();
      }
    } catch (error) {
      console.error('Error fetching next question:', error);
      // Fallback to page refresh if API fails
      router.refresh();
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLevels = () => {
    router.push('/');
  };

  const handleResetScore = () => {
    resetScore();
  };

  // Show loading when generating next question or when currentQuestion is null
  if (isLoading || !quizState.currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={score}
          onResetScore={handleResetScore}
        />
        <div className="pt-24">
          <SkeletonLoader variant="question" />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  if (quizState.currentQuestion && quizState.showResult && quizState.questionResult) {
    const isCorrect = quizState.questionResult.isCorrect;
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={score}
          onResetScore={handleResetScore}
        />
        <div className={`pt-24 transition-opacity duration-300 ${quizState.isTransitioning ? 'opacity-0' : 'opacity-100'}`}>
          <ResultCard
            question={{
              ...quizState.currentQuestion,
              correctAnswer: quizState.questionResult.correctAnswer,
              explanation: quizState.questionResult.explanation
            }}
            selectedAnswer={quizState.selectedAnswer}
            isCorrect={isCorrect}
            onNextQuestion={handleNextQuestion}
            score={score}
          />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  if (quizState.currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={score}
          onResetScore={handleResetScore}
        />
        <div className={`pt-24 transition-opacity duration-300 ${quizState.isTransitioning ? 'opacity-0' : 'opacity-100'}`}>
          <QuestionCard
            question={quizState.currentQuestion}
            selectedAnswer={quizState.selectedAnswer}
            onAnswerSelect={handleAnswerSelect}
            showResult={quizState.showResult}
          />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  // This should never be reached due to the loading check above
  return <LoadingSpinner />;
}
