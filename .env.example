# MongoDB Connection String
# Replace with your actual MongoDB connection string
MONG<PERSON>B_URI=mongodb://localhost:27017/infinite_language_db

# JWT Secret for authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key
# MongoDB Connection String
# Replace with your actual MongoDB connection string
MONGODB_URI=mongodb+srv://chinhluu299:<EMAIL>/infinite_language_db?retryWrites=true&w=majority

# JWT Secret for authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key

# Google OAuth
# Get these from Google Cloud Console
GOOGLE_CLIENT_ID=599702896254-696paahvoi6quek4bptm3hoe348gv13i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Facebook OAuth
# Get these from Facebook Developers
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Gemini AI - Multiple keys for better rate limit handling
# Get these from Google AI Studio: https://makersuite.google.com/app/apikey
# Separate multiple keys with commas (recommended: 3-5 keys)
GEMINI_API_KEY=AIzaSyCWnYPSaCqM-NiK8qKjrYd_ciCRWpWQfZM,AIzaSyAnotherKey123456789,AIzaSyThirdKey987654321,AIzaSyC4TGjtVCX_ICY2lgnk9FCW622L7Dm_clU
# Google OAuth
# Get these from Google Cloud Console
GOOGLE_CLIENT_ID=599702896254-696paahvoi6quek4bptm3hoe348gv13i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Facebook OAuth
# Get these from Facebook Developers
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Gemini AI
# Get this from Google AI Studio: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=AIzaSyCWnYPSaCqM-NiK8qKjrYd_ciCRWpWQfZM
